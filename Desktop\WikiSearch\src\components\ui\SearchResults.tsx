import Link from 'next/link';
import Image from 'next/image';
import { WikiSearchResult } from '@/lib/wiki';
import { truncateText } from '@/lib/utils';

interface SearchResultsProps {
  results: WikiSearchResult[];
}

export default function SearchResults({ results }: SearchResultsProps) {
  if (results.length === 0) {
    return (
      <div className="rounded-lg border p-8 text-center">
        <h2 className="text-xl font-semibold">No results found</h2>
        <p className="mt-2 text-muted-foreground">
          Try searching with different keywords.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {results.map((result) => (
        <Link
          key={result.pageid}
          href={`/s/${result.pageid}`}
          className="block rounded-lg border p-4 transition-colors hover:bg-accent/50"
        >
          <div className="flex gap-4">
            {result.thumbnail && (
              <div className="relative h-24 w-24 flex-shrink-0 overflow-hidden rounded-md">
                <Image
                  src={result.thumbnail.source}
                  alt={result.title}
                  fill
                  className="object-cover"
                  sizes="96px"
                />
              </div>
            )}
            <div className="flex-1">
              <h2 className="text-xl font-semibold">{result.title}</h2>
              <p className="mt-2 text-muted-foreground">
                {truncateText(result.extract, 200)}
              </p>
            </div>
          </div>
        </Link>
      ))}
    </div>
  );
}