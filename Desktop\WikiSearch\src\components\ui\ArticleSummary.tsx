import { Lightbulb } from 'lucide-react';

interface ArticleSummaryProps {
  summary: string;
}

export default function ArticleSummary({ summary }: ArticleSummaryProps) {
  return (
    <div className="rounded-lg border bg-accent/20 p-6">
      <div className="mb-4 flex items-center gap-2">
        <Lightbulb className="h-5 w-5 text-primary" />
        <h2 className="text-xl font-semibold">AI Summary</h2>
      </div>
      <div className="prose prose-sm dark:prose-invert">
        <p>{summary}</p>
      </div>
    </div>
  );
}