{"name": "wikisearch", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "groq-sdk": "^0.3.2", "lucide-react": "^0.363.0", "next": "14.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20.11.30", "@types/react": "^18.2.67", "@types/react-dom": "^18.2.22", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-next": "14.2.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.1", "typescript": "^5.4.5"}}