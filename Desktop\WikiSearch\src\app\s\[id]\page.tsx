import { getArticleDetails } from '@/lib/wiki';
import { getAISummary } from '@/lib/ai';
import Image from 'next/image';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import ArticleSummary from '@/components/ui/ArticleSummary';

interface ArticlePageProps {
  params: {
    id: string;
  };
}

export async function generateMetadata({
  params,
}: ArticlePageProps): Promise<Metadata> {
  try {
    const article = await getArticleDetails(params.id);
    return {
      title: `${article.title} - WikiSearch`,
      description: article.extract,
    };
  } catch (error) {
    return {
      title: 'Article Not Found - WikiSearch',
      description: 'The requested article could not be found.',
    };
  }
}

export default async function ArticlePage({ params }: ArticlePageProps) {
  try {
    const article = await getArticleDetails(params.id);
    const summary = await getAISummary(article.extract);

    return (
      <main className="container mx-auto py-8">
        <article className="mx-auto max-w-4xl">
          <header className="mb-8">
            <h1 className="mb-4 text-3xl font-bold md:text-4xl">{article.title}</h1>
            {article.thumbnail && (
              <div className="relative mb-6 h-[300px] w-full overflow-hidden rounded-lg md:h-[400px]">
                <Image
                  src={article.thumbnail.source}
                  alt={article.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 800px"
                  priority
                />
              </div>
            )}
          </header>

          <div className="prose prose-lg max-w-none dark:prose-invert">
            <div dangerouslySetInnerHTML={{ __html: article.extract_html }} />
          </div>

          <div className="mt-12">
            <ArticleSummary summary={summary} />
          </div>
        </article>
      </main>
    );
  } catch (error) {
    notFound();
  }
}