import SearchForm from '@/components/forms/SearchForm';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'WikiSearch - Home',
  description: 'Search Wikipedia articles and get AI-powered summaries',
};

export default function Home() {
  return (
    <main className="flex min-h-[calc(100vh-64px)] flex-col items-center justify-center p-4 md:p-24">
      <div className="w-full max-w-3xl text-center">
        <h1 className="mb-6 text-4xl font-extrabold tracking-tight md:text-6xl">
          Wiki<span className="text-primary">Search</span>
        </h1>
        <p className="mb-12 text-xl text-muted-foreground">
          Search Wikipedia articles and get AI-powered summaries
        </p>
        <SearchForm />
      </div>
    </main>
  );
}