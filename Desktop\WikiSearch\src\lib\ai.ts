import { Groq } from 'groq-sdk';
import { cache } from 'react';

// Create a cache for summaries
const summaryCache = new Map<string, string>();

export const getAISummary = cache(async (text: string): Promise<string> => {
  // Generate a cache key based on the text
  const cacheKey = Buffer.from(text).toString('base64');

  // Check if we have a cached summary
  if (summaryCache.has(cacheKey)) {
    return summaryCache.get(cacheKey) as string;
  }

  try {
    const groq = new Groq({
      apiKey: process.env.GROQ_API_KEY,
    });

    const prompt = `
      You are an expert at summarizing Wikipedia articles. 
      Please provide a concise, informative summary of the following Wikipedia article excerpt.
      Focus on the key points, main ideas, and significant details.
      Write in a clear, engaging style that helps the reader understand the topic quickly.
      
      Article text:
      ${text}
      
      Summary:
    `;

    const chatCompletion = await groq.chat.completions.create({
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
      model: 'llama3-8b-8192',
      temperature: 0.5,
      max_tokens: 500,
    });

    const summary = chatCompletion.choices[0]?.message?.content || 'Unable to generate summary.';
    
    // Cache the summary
    summaryCache.set(cacheKey, summary);
    
    return summary;
  } catch (error) {
    console.error('Error generating AI summary:', error);
    return 'Unable to generate a summary at this time. Please try again later.';
  }
});