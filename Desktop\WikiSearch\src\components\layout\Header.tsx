'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';

export default function Header() {
  const pathname = usePathname();
  const isHomePage = pathname === '/';

  return (
    <header className={cn(
      'sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur',
      isHomePage && 'bg-transparent border-transparent'
    )}>
      <div className="container flex h-16 items-center">
        <Link href="/" className="flex items-center space-x-2">
          <span className="text-xl font-bold">
            Wiki<span className="text-primary">Search</span>
          </span>
        </Link>
        <nav className="ml-auto flex items-center space-x-4">
          <Link
            href="/"
            className={cn(
              'text-sm font-medium transition-colors hover:text-primary',
              pathname === '/' ? 'text-foreground' : 'text-foreground/60'
            )}
          >
            Home
          </Link>
        </nav>
      </div>
    </header>
  );
}