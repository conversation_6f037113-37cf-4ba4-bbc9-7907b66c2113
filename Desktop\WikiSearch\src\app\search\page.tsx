import SearchForm from '@/components/forms/SearchForm';
import SearchResults from '@/components/ui/SearchResults';
import { searchWikipedia } from '@/lib/wiki';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'WikiSearch - Results',
  description: 'Search results from Wikipedia',
};

export default async function SearchPage({
  searchParams,
}: {
  searchParams: { q: string };
}) {
  const query = searchParams.q || '';
  const results = query ? await searchWikipedia(query) : [];

  return (
    <main className="container mx-auto py-8">
      <div className="mb-8">
        <SearchForm defaultValue={query} />
      </div>
      
      {query ? (
        <>
          <h1 className="mb-6 text-2xl font-bold">
            Search results for: <span className="text-primary">{query}</span>
          </h1>
          <SearchResults results={results} />
        </>
      ) : (
        <div className="rounded-lg border p-8 text-center">
          <h2 className="text-xl font-semibold">No search query provided</h2>
          <p className="mt-2 text-muted-foreground">
            Please enter a search term to find Wikipedia articles.
          </p>
        </div>
      )}
    </main>
  );
}