import { cache } from 'react';

export interface WikiSearchResult {
  pageid: number;
  title: string;
  extract: string;
  thumbnail?: {
    source: string;
    width: number;
    height: number;
  };
}

export interface WikiArticle extends WikiSearchResult {
  extract_html: string;
}

// Cache the search results
export const searchWikipedia = cache(async (query: string): Promise<WikiSearchResult[]> => {
  try {
    const params = new URLSearchParams({
      action: 'query',
      format: 'json',
      list: 'search',
      srsearch: query,
      srlimit: '10',
      srprop: 'snippet',
      origin: '*',
    });

    const searchResponse = await fetch(`https://en.wikipedia.org/w/api.php?${params}`);
    const searchData = await searchResponse.json();

    if (!searchData.query?.search) {
      return [];
    }

    // Get page IDs for the search results
    const pageIds = searchData.query.search.map((result: any) => result.pageid).join('|');

    // Fetch additional details for the search results
    const detailsParams = new URLSearchParams({
      action: 'query',
      format: 'json',
      prop: 'extracts|pageimages',
      pageids: pageIds,
      exintro: '1',
      explaintext: '1',
      piprop: 'thumbnail',
      pithumbsize: '300',
      origin: '*',
    });

    const detailsResponse = await fetch(`https://en.wikipedia.org/w/api.php?${detailsParams}`);
    const detailsData = await detailsResponse.json();

    // Combine search results with additional details
    return searchData.query.search.map((result: any) => {
      const pageDetails = detailsData.query.pages[result.pageid];
      return {
        pageid: result.pageid,
        title: result.title,
        extract: pageDetails.extract || result.snippet.replace(/<[^>]*>/g, ''),
        thumbnail: pageDetails.thumbnail,
      };
    });
  } catch (error) {
    console.error('Error searching Wikipedia:', error);
    return [];
  }
});

// Cache the article details
export const getArticleDetails = cache(async (pageId: string): Promise<WikiArticle> => {
  try {
    const params = new URLSearchParams({
      action: 'query',
      format: 'json',
      prop: 'extracts|pageimages',
      pageids: pageId,
      exintro: '1',
      piprop: 'thumbnail',
      pithumbsize: '800',
      origin: '*',
    });

    const response = await fetch(`https://en.wikipedia.org/w/api.php?${params}`);
    const data = await response.json();

    if (!data.query?.pages?.[pageId]) {
      throw new Error('Article not found');
    }

    const article = data.query.pages[pageId];

    // Get HTML extract
    const htmlParams = new URLSearchParams({
      action: 'query',
      format: 'json',
      prop: 'extracts',
      pageids: pageId,
      exintro: '1',
      origin: '*',
    });

    const htmlResponse = await fetch(`https://en.wikipedia.org/w/api.php?${htmlParams}`);
    const htmlData = await htmlResponse.json();
    const extract_html = htmlData.query.pages[pageId].extract;

    return {
      pageid: parseInt(pageId),
      title: article.title,
      extract: article.extract,
      extract_html,
      thumbnail: article.thumbnail,
    };
  } catch (error) {
    console.error('Error fetching article details:', error);
    throw error;
  }
});